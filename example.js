import { chatStream } from './app.js';

// Example 4: Streaming chat with conversation history
async function streamingConversationChat() {
    let fullResponse = ''; // Initialize the variable to store the complete response

const messages = [
    { 
        role: 'system', 
        content: 'who are you ? you have to explor your self' 
    },
    { role: 'user', content: 'Hello there ? who are you ?' },
];


    await chatStream(
        'airforce',                       // provider
        'grok-4',                        // model
        messages,                         // conversation history
        (chunk) => {                      // onChunk callback
            if (chunk.choices?.[0]?.delta?.content) {
                const content = chunk.choices[0].delta.content;
                process.stdout.write(content); // Stream to console
                fullResponse += content;
            }
        },
        { temperature: 0.7 }              // options
    );
}

streamingConversationChat()