import { chatStream } from './app.js';

// Example 4: Streaming chat with conversation history
async function streamingConversationChat() {
    let fullResponse = ''; // Initialize the variable to store the complete response

const messages = [
    { 
        role: 'system', 
        content: 'who are you ? you have to explor your self' 
    },
    { role: 'user', content: 'Hello there ? who are you ?' },
];


    await chatStream(
        'airforce',                       // provider
        'gpt-5-mini',                        // model
        messages,                         // conversation history
        strem = true,
        { temperature: 0.7 }              // options
    );
}

streamingConversationChat()