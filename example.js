import { chatStream } from './app.js';

// Enhanced streaming chat with performance optimizations
async function streamingConversationChat() {
    let fullResponse = ''; // Store complete response
    let chunkCount = 0;    // Track number of chunks received
    let firstChunkTime = null; // Track time to first chunk
    let totalStartTime = Date.now(); // Track total response time

    const messages = [
        {
            role: 'system',
            content: 'who are you ? you have to explor your self'
        },
        { role: 'user', content: 'Hello there ? who are you ?' },
    ];

    console.log('🚀 Starting streaming chat...\n');

    try {
        await chatStream(
            'airforce',                       // provider
            'gpt-4o-mini',                   // Using faster model for better performance
            messages,                         // conversation history
            (chunk) => {                      // Enhanced onChunk callback
                chunkCount++;

                // Record time to first chunk
                if (firstChunkTime === null) {
                    firstChunkTime = Date.now() - totalStartTime;
                    console.log(`⚡ First chunk received in ${firstChunkTime}ms\n`);
                }

                // Process chunk content immediately for faster perceived response
                if (chunk.choices?.[0]?.delta?.content) {
                    const content = chunk.choices[0].delta.content;

                    // Immediate output for faster perceived response
                    process.stdout.write(content);
                    fullResponse += content;
                }

                // Handle reasoning content if available
                if (chunk.choices?.[0]?.delta?.reasoning_content) {
                    const reasoning = chunk.choices[0].delta.reasoning_content;
                    // Optional: Show reasoning (uncomment if needed)
                    // process.stdout.write(`\n[🧠 ${reasoning}]\n`);
                }
            },
            {
                temperature: 0.5,            // Lower temperature for faster response
                max_tokens: 1024,            // Limit tokens for faster completion
                stream: true,                // Ensure streaming is enabled
                // Performance optimizations
                top_p: 0.9,                  // Nucleus sampling for faster generation
                frequency_penalty: 0,        // No penalty for faster processing
                presence_penalty: 0          // No penalty for faster processing
            }
        );

        // Final statistics
        const totalTime = Date.now() - totalStartTime;
        console.log(`\n\n📈 Streaming completed:`);
        console.log(`   • Total time: ${totalTime}ms`);
        console.log(`   • Time to first chunk: ${firstChunkTime}ms`);
        console.log(`   • Total chunks: ${chunkCount}`);
        console.log(`   • Average chunk time: ${(totalTime / chunkCount).toFixed(1)}ms`);
        console.log(`   • Response length: ${fullResponse.length} characters`);
        console.log(`   • Streaming speed: ${(fullResponse.length / (totalTime / 1000)).toFixed(1)} chars/sec`);

    } catch (error) {
        console.error('\n❌ Streaming error:', error.message);

        // Fallback: Try with different configuration
        console.log('\n🔄 Trying fallback configuration...');
        await streamingFallback(messages);
    }
}

// Fallback function with alternative configuration
async function streamingFallback(messages) {
    console.log('🔄 Using fallback streaming with optimized settings...\n');

    let fallbackResponse = '';
    const startTime = Date.now();

    try {
        await chatStream(
            'airforce',                       // Same provider
            'gpt-4o-mini',                   // Fast model
            messages,
            (chunk) => {
                if (chunk.choices?.[0]?.delta?.content) {
                    const content = chunk.choices[0].delta.content;
                    process.stdout.write(content);
                    fallbackResponse += content;
                }
            },
            {
                temperature: 0.3,            // Even lower temperature for speed
                max_tokens: 512,             // Shorter response for speed
                stream: true,
                top_p: 0.8                   // More focused sampling
            }
        );

        const fallbackTime = Date.now() - startTime;
        console.log(`\n\n✅ Fallback completed in ${fallbackTime}ms`);

    } catch (fallbackError) {
        console.error('\n❌ Fallback also failed:', fallbackError.message);
    }
}

// Enhanced function with multiple optimization strategies
async function optimizedStreamingChat() {
    console.log('🎯 Running optimized streaming with multiple strategies...\n');

    const messages = [
        {
            role: 'system',
            content: 'Respond concisely and efficiently. Prioritize speed while maintaining quality.'
        },
        { role: 'user', content: 'Hello there ? who are you ?' },
    ];

    // Strategy configurations for different speed/quality tradeoffs
    const strategies = [
        {
            name: 'Ultra Fast',
            provider: 'airforce',
            model: 'gpt-4o-mini',
            options: { temperature: 0.2, max_tokens: 256, top_p: 0.7 }
        },
        {
            name: 'Balanced Speed',
            provider: 'airforce',
            model: 'gpt-4o-mini',
            options: { temperature: 0.4, max_tokens: 512, top_p: 0.8 }
        },
        {
            name: 'Quality Focus',
            provider: 'airforce',
            model: 'gpt-4o',
            options: { temperature: 0.6, max_tokens: 1024, top_p: 0.9 }
        }
    ];

    // Try strategies in order of speed
    for (const strategy of strategies) {
        console.log(`🚀 Trying ${strategy.name} strategy...`);

        try {
            let response = '';
            let chunkCount = 0;
            const startTime = Date.now();
            let firstChunk = null;

            await chatStream(
                strategy.provider,
                strategy.model,
                messages,
                (chunk) => {
                    chunkCount++;
                    if (firstChunk === null) {
                        firstChunk = Date.now() - startTime;
                    }

                    if (chunk.choices?.[0]?.delta?.content) {
                        const content = chunk.choices[0].delta.content;
                        process.stdout.write(content);
                        response += content;
                    }
                },
                { ...strategy.options, stream: true }
            );

            const totalTime = Date.now() - startTime;
            console.log(`\n\n✅ ${strategy.name} completed successfully!`);
            console.log(`   • Total time: ${totalTime}ms`);
            console.log(`   • First chunk: ${firstChunk}ms`);
            console.log(`   • Chunks: ${chunkCount}`);
            console.log(`   • Speed: ${(response.length / (totalTime / 1000)).toFixed(1)} chars/sec\n`);

            return; // Success, exit

        } catch (error) {
            console.log(`❌ ${strategy.name} failed: ${error.message}`);
            console.log('🔄 Trying next strategy...\n');
        }
    }

    console.log('❌ All strategies failed');
}

// Run the enhanced streaming function
console.log('='.repeat(60));
console.log('🚀 ENHANCED STREAMING CHAT DEMO');
console.log('='.repeat(60));

// Run the main function
streamingConversationChat()
    .then(() => {
        console.log('\n' + '='.repeat(60));
        console.log('🎯 Running optimized version...');
        console.log('='.repeat(60));
        return optimizedStreamingChat();
    })
    .catch(error => {
        console.error('❌ Demo failed:', error);
    });