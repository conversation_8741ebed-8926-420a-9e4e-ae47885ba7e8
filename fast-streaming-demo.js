import { 
    optimizedChatStream, 
    adaptiveStreamingChat, 
    resilientStreamingChat,
    batchStreamingChat,
    STREAMING_CONFIGS 
} from './streaming-optimizations.js';

/**
 * Demo of various streaming optimization techniques
 */

async function runFastStreamingDemo() {
    console.log('🚀 FAST STREAMING OPTIMIZATION DEMO');
    console.log('='.repeat(50));
    
    const prompt = "Explain quantum computing in simple terms";
    const provider = 'airforce';
    
    // Demo 1: Ultra-fast streaming
    console.log('\n1️⃣ ULTRA-FAST STREAMING');
    console.log('-'.repeat(30));
    
    try {
        const result1 = await optimizedChatStream(provider, prompt, {
            config: 'ultraFast',
            preferSpeed: true,
            onChunk: (content) => {
                process.stdout.write(content);
            }
        });
        
        console.log(`\n📊 Ultra-fast stats: ${result1.stats.charsPerSecond.toFixed(1)} chars/sec`);
        
    } catch (error) {
        console.error('❌ Ultra-fast demo failed:', error.message);
    }
    
    // Demo 2: Adaptive streaming
    console.log('\n\n2️⃣ ADAPTIVE STREAMING');
    console.log('-'.repeat(30));
    
    try {
        const result2 = await adaptiveStreamingChat(provider, prompt, {
            model: 'gpt-4o-mini',
            onChunk: (content) => {
                process.stdout.write(content);
            }
        });
        
        console.log(`\n📊 Adaptive stats: ${result2.stats.charsPerSecond.toFixed(1)} chars/sec`);
        console.log(`💡 Recommended config: ${result2.recommendedConfig}`);
        
    } catch (error) {
        console.error('❌ Adaptive demo failed:', error.message);
    }
    
    // Demo 3: Resilient streaming with retries
    console.log('\n\n3️⃣ RESILIENT STREAMING');
    console.log('-'.repeat(30));
    
    try {
        const result3 = await resilientStreamingChat(provider, prompt, {
            config: 'fast',
            maxRetries: 3,
            onChunk: (content) => {
                process.stdout.write(content);
            }
        });
        
        console.log(`\n📊 Resilient stats: ${result3.stats.charsPerSecond.toFixed(1)} chars/sec`);
        
    } catch (error) {
        console.error('❌ Resilient demo failed:', error.message);
    }
    
    // Demo 4: Batch processing
    console.log('\n\n4️⃣ BATCH STREAMING');
    console.log('-'.repeat(30));
    
    const batchPrompts = [
        "What is AI?",
        "Explain machine learning",
        "What is deep learning?"
    ];
    
    try {
        const batchResults = await batchStreamingChat(provider, batchPrompts, {
            config: 'fast',
            batchSize: 2,
            onChunk: (content) => {
                process.stdout.write(content);
            }
        });
        
        console.log(`\n📊 Batch completed: ${batchResults.length} responses`);
        batchResults.forEach((result, i) => {
            if (result.stats) {
                console.log(`   Response ${i+1}: ${result.stats.charsPerSecond.toFixed(1)} chars/sec`);
            }
        });
        
    } catch (error) {
        console.error('❌ Batch demo failed:', error.message);
    }
    
    console.log('\n' + '='.repeat(50));
    console.log('✅ Demo completed!');
}

// Performance comparison function
async function compareStreamingPerformance() {
    console.log('\n🏁 PERFORMANCE COMPARISON');
    console.log('='.repeat(50));
    
    const prompt = "Write a short story about a robot";
    const provider = 'airforce';
    const configs = ['ultraFast', 'fast', 'balanced'];
    
    for (const config of configs) {
        console.log(`\n🔧 Testing ${config.toUpperCase()} configuration:`);
        console.log('-'.repeat(40));
        
        try {
            const startTime = Date.now();
            let charCount = 0;
            
            const result = await optimizedChatStream(provider, prompt, {
                config: config,
                onChunk: (content) => {
                    charCount += content.length;
                    // Show dots instead of full content for comparison
                    process.stdout.write('.');
                }
            });
            
            const totalTime = Date.now() - startTime;
            console.log(`\n📊 ${config} results:`);
            console.log(`   • Total time: ${totalTime}ms`);
            console.log(`   • Characters: ${charCount}`);
            console.log(`   • Speed: ${result.stats.charsPerSecond.toFixed(1)} chars/sec`);
            console.log(`   • First chunk: ${result.stats.firstChunkTime}ms`);
            
        } catch (error) {
            console.error(`❌ ${config} failed:`, error.message);
        }
        
        // Small delay between tests
        await new Promise(resolve => setTimeout(resolve, 500));
    }
}

// Real-time streaming with live statistics
async function liveStreamingWithStats() {
    console.log('\n📊 LIVE STREAMING WITH STATISTICS');
    console.log('='.repeat(50));
    
    const prompt = "Explain the benefits of renewable energy";
    const provider = 'airforce';
    
    let chunkCount = 0;
    let totalChars = 0;
    const startTime = Date.now();
    
    try {
        await optimizedChatStream(provider, prompt, {
            config: 'fast',
            onChunk: (content, chunk) => {
                chunkCount++;
                totalChars += content.length;
                
                // Show content
                process.stdout.write(content);
                
                // Show live stats every 10 chunks
                if (chunkCount % 10 === 0) {
                    const elapsed = Date.now() - startTime;
                    const speed = totalChars / (elapsed / 1000);
                    process.stdout.write(`\n[📊 Chunk ${chunkCount}, Speed: ${speed.toFixed(1)} chars/sec]\n`);
                }
            }
        });
        
        const totalTime = Date.now() - startTime;
        console.log(`\n\n🎯 Final statistics:`);
        console.log(`   • Total chunks: ${chunkCount}`);
        console.log(`   • Total characters: ${totalChars}`);
        console.log(`   • Total time: ${totalTime}ms`);
        console.log(`   • Average speed: ${(totalChars / (totalTime / 1000)).toFixed(1)} chars/sec`);
        
    } catch (error) {
        console.error('❌ Live streaming failed:', error.message);
    }
}

// Main execution
async function main() {
    try {
        await runFastStreamingDemo();
        await compareStreamingPerformance();
        await liveStreamingWithStats();
        
        console.log('\n🎉 All demos completed successfully!');
        console.log('\n💡 Tips for faster streaming:');
        console.log('   • Use "ultraFast" config for maximum speed');
        console.log('   • Choose gpt-4o-mini for fastest responses');
        console.log('   • Lower temperature (0.1-0.3) for faster generation');
        console.log('   • Limit max_tokens for quicker completion');
        console.log('   • Use top_p < 0.8 for more focused responses');
        
    } catch (error) {
        console.error('❌ Demo failed:', error);
    }
}

// Run the demo
main();
