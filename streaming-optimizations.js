import { chatStream } from './app.js';

/**
 * Advanced streaming optimizations for faster AI responses
 * This module provides various techniques to improve streaming performance
 */

// Performance monitoring utilities
class StreamingPerformanceMonitor {
    constructor() {
        this.reset();
    }
    
    reset() {
        this.startTime = Date.now();
        this.firstChunkTime = null;
        this.chunkCount = 0;
        this.totalChars = 0;
        this.chunkTimes = [];
    }
    
    recordChunk(content) {
        const now = Date.now();
        this.chunkCount++;
        
        if (this.firstChunkTime === null) {
            this.firstChunkTime = now - this.startTime;
        }
        
        if (content) {
            this.totalChars += content.length;
        }
        
        this.chunkTimes.push(now - this.startTime);
    }
    
    getStats() {
        const totalTime = Date.now() - this.startTime;
        return {
            totalTime,
            firstChunkTime: this.firstChunkTime,
            chunkCount: this.chunkCount,
            totalChars: this.totalChars,
            avgChunkTime: this.chunkCount > 0 ? totalTime / this.chunkCount : 0,
            charsPerSecond: this.totalChars / (totalTime / 1000),
            chunksPerSecond: this.chunkCount / (totalTime / 1000)
        };
    }
}

// Optimized streaming configurations
const STREAMING_CONFIGS = {
    ultraFast: {
        temperature: 0.1,
        max_tokens: 256,
        top_p: 0.6,
        frequency_penalty: 0,
        presence_penalty: 0,
        stream: true
    },
    
    fast: {
        temperature: 0.3,
        max_tokens: 512,
        top_p: 0.7,
        frequency_penalty: 0,
        presence_penalty: 0,
        stream: true
    },
    
    balanced: {
        temperature: 0.5,
        max_tokens: 1024,
        top_p: 0.8,
        frequency_penalty: 0.1,
        presence_penalty: 0.1,
        stream: true
    },
    
    quality: {
        temperature: 0.7,
        max_tokens: 2048,
        top_p: 0.9,
        frequency_penalty: 0.2,
        presence_penalty: 0.2,
        stream: true
    }
};

// Model performance rankings (fastest to slowest)
const MODEL_PERFORMANCE = {
    'gpt-4o-mini': { speed: 'ultra-fast', quality: 'good' },
    'gpt-4o': { speed: 'fast', quality: 'excellent' },
    'gpt-3.5-turbo': { speed: 'fast', quality: 'good' },
    'claude-3-haiku': { speed: 'fast', quality: 'good' },
    'gemini-pro': { speed: 'medium', quality: 'excellent' },
    'llama-3.1-8b': { speed: 'fast', quality: 'good' },
    'llama-3.1-70b': { speed: 'medium', quality: 'excellent' }
};

/**
 * Optimized streaming function with automatic fallbacks
 */
export async function optimizedChatStream(provider, prompt, options = {}) {
    const monitor = new StreamingPerformanceMonitor();
    const config = options.config || 'fast';
    const streamConfig = STREAMING_CONFIGS[config];
    
    // Prepare messages
    const messages = Array.isArray(prompt) ? prompt : [
        { role: 'user', content: prompt }
    ];
    
    // Model selection based on performance requirements
    const preferredModels = options.preferSpeed ? 
        ['gpt-4o-mini', 'gpt-3.5-turbo', 'gpt-4o'] :
        ['gpt-4o', 'gpt-4o-mini', 'gpt-3.5-turbo'];
    
    let response = '';
    let success = false;
    
    // Try models in order of preference
    for (const model of preferredModels) {
        if (success) break;
        
        console.log(`🚀 Trying ${model} with ${config} config...`);
        monitor.reset();
        
        try {
            await chatStream(
                provider,
                model,
                messages,
                (chunk) => {
                    const content = chunk.choices?.[0]?.delta?.content;
                    if (content) {
                        monitor.recordChunk(content);
                        
                        // Immediate output for perceived speed
                        if (options.onChunk) {
                            options.onChunk(content, chunk);
                        } else {
                            process.stdout.write(content);
                        }
                        
                        response += content;
                    }
                },
                { ...streamConfig, ...options.overrides }
            );
            
            success = true;
            const stats = monitor.getStats();
            
            console.log(`\n✅ Success with ${model}!`);
            console.log(`📊 Performance: ${stats.charsPerSecond.toFixed(1)} chars/sec, First chunk: ${stats.firstChunkTime}ms`);
            
        } catch (error) {
            console.log(`❌ ${model} failed: ${error.message}`);
        }
    }
    
    if (!success) {
        throw new Error('All models failed');
    }
    
    return {
        response,
        stats: monitor.getStats()
    };
}

/**
 * Batch streaming for multiple prompts
 */
export async function batchStreamingChat(provider, prompts, options = {}) {
    const results = [];
    const batchSize = options.batchSize || 3;
    
    console.log(`🔄 Processing ${prompts.length} prompts in batches of ${batchSize}...`);
    
    for (let i = 0; i < prompts.length; i += batchSize) {
        const batch = prompts.slice(i, i + batchSize);
        const batchPromises = batch.map(async (prompt, index) => {
            console.log(`\n📝 Processing prompt ${i + index + 1}/${prompts.length}:`);
            console.log(`"${prompt.substring(0, 50)}${prompt.length > 50 ? '...' : '"}"`);
            
            try {
                return await optimizedChatStream(provider, prompt, {
                    ...options,
                    config: 'fast' // Use fast config for batch processing
                });
            } catch (error) {
                console.error(`❌ Batch item ${i + index + 1} failed:`, error.message);
                return { response: '', error: error.message };
            }
        });
        
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
        
        // Small delay between batches to avoid rate limiting
        if (i + batchSize < prompts.length) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }
    }
    
    return results;
}

/**
 * Adaptive streaming that adjusts parameters based on response speed
 */
export async function adaptiveStreamingChat(provider, prompt, options = {}) {
    const monitor = new StreamingPerformanceMonitor();
    let currentConfig = 'balanced';
    let response = '';
    
    console.log('🎯 Starting adaptive streaming...');
    
    const messages = Array.isArray(prompt) ? prompt : [
        { role: 'user', content: prompt }
    ];
    
    try {
        await chatStream(
            provider,
            options.model || 'gpt-4o-mini',
            messages,
            (chunk) => {
                const content = chunk.choices?.[0]?.delta?.content;
                if (content) {
                    monitor.recordChunk(content);
                    
                    // Adaptive logic: if response is slow, suggest faster config
                    if (monitor.chunkCount === 5) { // Check after 5 chunks
                        const stats = monitor.getStats();
                        if (stats.avgChunkTime > 200) { // If chunks are slow
                            console.log('\n⚡ Slow response detected, consider using "ultraFast" config next time');
                        }
                    }
                    
                    if (options.onChunk) {
                        options.onChunk(content, chunk);
                    } else {
                        process.stdout.write(content);
                    }
                    
                    response += content;
                }
            },
            STREAMING_CONFIGS[currentConfig]
        );
        
        const stats = monitor.getStats();
        console.log(`\n📊 Adaptive streaming completed:`);
        console.log(`   • Speed: ${stats.charsPerSecond.toFixed(1)} chars/sec`);
        console.log(`   • Recommended config for next time: ${stats.charsPerSecond > 50 ? 'quality' : 'ultraFast'}`);
        
        return { response, stats, recommendedConfig: stats.charsPerSecond > 50 ? 'quality' : 'ultraFast' };
        
    } catch (error) {
        console.error('❌ Adaptive streaming failed:', error.message);
        throw error;
    }
}

/**
 * Streaming with automatic retry and exponential backoff
 */
export async function resilientStreamingChat(provider, prompt, options = {}) {
    const maxRetries = options.maxRetries || 3;
    const baseDelay = options.baseDelay || 1000;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            console.log(`🔄 Attempt ${attempt}/${maxRetries}...`);
            
            return await optimizedChatStream(provider, prompt, {
                ...options,
                config: attempt === 1 ? 'fast' : 'ultraFast' // Use faster config on retries
            });
            
        } catch (error) {
            console.log(`❌ Attempt ${attempt} failed: ${error.message}`);
            
            if (attempt < maxRetries) {
                const delay = baseDelay * Math.pow(2, attempt - 1);
                console.log(`⏳ Waiting ${delay}ms before retry...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            } else {
                throw new Error(`All ${maxRetries} attempts failed. Last error: ${error.message}`);
            }
        }
    }
}

// Export configurations for external use
export { STREAMING_CONFIGS, MODEL_PERFORMANCE, StreamingPerformanceMonitor };
