import { chatStream, fastChatStream } from './app.js';

// 🚀 SIMPLE FAST STREAMING EXAMPLES
// Just set stream: true and get optimized performance!

// Example 1: Regular streaming with auto-optimizations
async function regularStreamingExample() {
    console.log('🔥 Regular Streaming (Auto-Optimized):');
    console.log('-'.repeat(40));
    
    await chatStream(
        'airforce',
        'gpt-4o-mini',
        'Tell me about artificial intelligence',
        (chunk) => {
            if (chunk.choices?.[0]?.delta?.content) {
                process.stdout.write(chunk.choices[0].delta.content);
            }
        },
        { 
            stream: true,        // Just set this to true!
            showStats: true      // See performance stats
        }
    );
}

// Example 2: Ultra-fast streaming
async function ultraFastStreamingExample() {
    console.log('\n\n⚡ Ultra-Fast Streaming:');
    console.log('-'.repeat(40));
    
    await chatStream(
        'airforce',
        'gpt-4o-mini',
        'Explain quantum computing briefly',
        (chunk) => {
            if (chunk.choices?.[0]?.delta?.content) {
                process.stdout.write(chunk.choices[0].delta.content);
            }
        },
        { 
            stream: true,
            speed: 'ultra-fast', // Maximum speed mode
            showStats: true
        }
    );
}

// Example 3: Using the fastChatStream helper (even simpler!)
async function fastChatStreamExample() {
    console.log('\n\n🎯 Fast Chat Stream Helper:');
    console.log('-'.repeat(40));
    
    await fastChatStream(
        'airforce',
        'gpt-4o-mini',
        'What is machine learning?',
        (chunk) => {
            if (chunk.choices?.[0]?.delta?.content) {
                process.stdout.write(chunk.choices[0].delta.content);
            }
        }
        // No options needed! Already optimized for speed
    );
}

// Example 4: Conversation with fast streaming
async function conversationExample() {
    console.log('\n\n💬 Fast Conversation:');
    console.log('-'.repeat(40));
    
    const messages = [
        { role: 'system', content: 'You are a helpful assistant. Be concise.' },
        { role: 'user', content: 'What are the benefits of renewable energy?' }
    ];
    
    await chatStream(
        'airforce',
        'gpt-4o-mini',
        messages,
        (chunk) => {
            if (chunk.choices?.[0]?.delta?.content) {
                process.stdout.write(chunk.choices[0].delta.content);
            }
        },
        { 
            stream: true,
            speed: 'fast',       // Fast mode
            showStats: true,
            autoRetry: true      // Auto-retry if fails
        }
    );
}

// Example 5: Different speed modes comparison
async function speedModesExample() {
    console.log('\n\n🏁 Speed Modes Comparison:');
    console.log('='.repeat(50));
    
    const prompt = 'Explain photosynthesis in 2 sentences';
    const modes = ['ultra-fast', 'fast', 'balanced'];
    
    for (const mode of modes) {
        console.log(`\n🔧 ${mode.toUpperCase()} mode:`);
        console.log('-'.repeat(30));
        
        await chatStream(
            'airforce',
            'gpt-4o-mini',
            prompt,
            (chunk) => {
                if (chunk.choices?.[0]?.delta?.content) {
                    process.stdout.write(chunk.choices[0].delta.content);
                }
            },
            { 
                stream: true,
                speed: mode,
                showStats: true
            }
        );
        
        console.log('\n');
    }
}

// Run all examples
async function runAllExamples() {
    try {
        await regularStreamingExample();
        await ultraFastStreamingExample();
        await fastChatStreamExample();
        await conversationExample();
        await speedModesExample();
        
        console.log('\n' + '='.repeat(50));
        console.log('✅ All examples completed!');
        console.log('\n💡 Quick Tips:');
        console.log('• Just add stream: true for auto-optimized streaming');
        console.log('• Use speed: "ultra-fast" for maximum speed');
        console.log('• Use fastChatStream() for pre-optimized streaming');
        console.log('• Add showStats: true to see performance metrics');
        console.log('• autoRetry: true automatically retries with faster settings');
        
    } catch (error) {
        console.error('❌ Error:', error.message);
    }
}

// Run the demo
runAllExamples();
