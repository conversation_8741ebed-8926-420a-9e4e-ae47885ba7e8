import { PollinationsAI, DeepInfra, Client } from './ai.js';

/**
 * Api.Airforce provider class
 */
class Airforce extends Client {
    constructor(options = {}) {
        super({
            baseUrl: 'https://api.airforce/v1',
            apiKey: options.apiKey || (typeof localStorage !== 'undefined' ? localStorage.getItem('ApiAirforce-api_key') : null),
            defaultModel: 'gpt-4o-mini',
            ...options
        });
    }
}

/**
 * AI App - Simple function-based interface for AI providers with streaming support
 */
class AIApp {
    constructor() {
        this.providers = {};
        this.initializeProviders();
    }

    /**
     * Initialize all available AI providers
     */
    initializeProviders() {
        // Only initialize providers that work without API keys in Node.js
        try {
            this.providers.pollinations = new PollinationsAI();
            console.log('✅ PollinationsAI initialized successfully');
        } catch (e) {
            console.warn('❌ PollinationsAI not available:', e.message);
        }

        try {
            this.providers.deepinfra = new DeepInfra();
            console.log('✅ DeepInfra initialized successfully');
        } catch (e) {
            console.warn('❌ DeepInfra not available:', e.message);
        }

        try {
            this.providers.airforce = new Airforce();
            console.log('✅ Api.Airforce initialized successfully');
        } catch (e) {
            console.warn('❌ Api.Airforce not available:', e.message);
        }

        // Log available providers
        const availableProviders = Object.keys(this.providers);
        if (availableProviders.length > 0) {
            console.log(`🚀 Available providers: ${availableProviders.join(', ')}`);
        } else {
            console.warn('⚠️  No providers available');
        }
    }

    /**
     * Get available providers
     * @returns {string[]} Array of available provider names
     */
    getAvailableProviders() {
        return Object.keys(this.providers);
    }

    /**
     * Get available models for a provider
     * @param {string} provider - Provider name
     * @returns {Promise<Array>} Array of available models
     */
    async getModels(provider) {
        if (!this.providers[provider]) {
            throw new Error(`Provider '${provider}' not available`);
        }

        try {
            const models = await this.providers[provider].models.list();
            return models;
        } catch (error) {
            console.error(`Failed to get models for ${provider}:`, error);
            return [];
        }
    }

    /**
     * Chat with AI (non-streaming)
     * @param {string} provider - Provider name (e.g., 'pollinations', 'together')
     * @param {string} model - Model name
     * @param {string|Array} prompt - Prompt string or messages array
     * @param {Object} options - Additional options
     * @returns {Promise<Object>} AI response
     */
    async chat(provider, model, prompt, options = {}) {
        if (!this.providers[provider]) {
            throw new Error(`Provider '${provider}' not available`);
        }

        const messages = this._formatMessages(prompt);
        
        const params = {
            model: model,
            messages: messages,
            stream: false,
            ...options
        };

        try {
            const response = await this.providers[provider].chat.completions.create(params);
            return response;
        } catch (error) {
            console.error(`Chat failed for ${provider}:`, error);
            throw error;
        }
    }

    /**
     * Chat with AI (streaming) - Enhanced with performance optimizations
     * @param {string} provider - Provider name
     * @param {string} model - Model name
     * @param {string|Array} prompt - Prompt string or messages array
     * @param {Function} onChunk - Callback for each chunk
     * @param {Object} options - Additional options
     * @returns {Promise<void>}
     */
    async chatStream(provider, model, prompt, onChunk, options = {}) {
        if (!this.providers[provider]) {
            throw new Error(`Provider '${provider}' not available`);
        }

        if (typeof onChunk !== 'function') {
            throw new Error('onChunk callback is required for streaming');
        }

        const messages = this._formatMessages(prompt);

        // Auto-optimize parameters for faster streaming
        const optimizedParams = this._getOptimizedStreamingParams(model, options);

        const params = {
            model: model,
            messages: messages,
            stream: true,
            ...optimizedParams,
            ...options // User options override optimizations
        };

        // Performance tracking
        const startTime = Date.now();
        let firstChunkTime = null;
        let chunkCount = 0;
        let totalChars = 0;

        try {
            const stream = await this.providers[provider].chat.completions.create(params);

            for await (const chunk of stream) {
                chunkCount++;

                // Track first chunk time
                if (firstChunkTime === null) {
                    firstChunkTime = Date.now() - startTime;
                    if (options.showStats) {
                        console.log(`⚡ First chunk in ${firstChunkTime}ms`);
                    }
                }

                // Track content for stats
                if (chunk.choices?.[0]?.delta?.content) {
                    totalChars += chunk.choices[0].delta.content.length;
                }

                // Call user's onChunk callback
                onChunk(chunk);
            }

            // Show final stats if requested
            if (options.showStats) {
                const totalTime = Date.now() - startTime;
                const speed = totalChars / (totalTime / 1000);
                console.log(`\n📊 Stream stats: ${speed.toFixed(1)} chars/sec, ${chunkCount} chunks, ${totalTime}ms total`);
            }

        } catch (error) {
            console.error(`Streaming chat failed for ${provider}:`, error);

            // Auto-retry with faster settings if enabled
            if (options.autoRetry !== false && !options._isRetry) {
                console.log('🔄 Retrying with optimized settings...');
                return this.chatStream(provider, this._getFastestModel(model), prompt, onChunk, {
                    ...options,
                    _isRetry: true,
                    temperature: 0.3,
                    max_tokens: 512,
                    top_p: 0.7
                });
            }

            throw error;
        }
    }

    /**
     * Get optimized streaming parameters based on model
     * @param {string} model - Model name
     * @param {Object} options - User options
     * @returns {Object} Optimized parameters
     */
    _getOptimizedStreamingParams(model, options = {}) {
        // Default fast streaming settings
        const baseParams = {
            temperature: 0.5,
            max_tokens: 1024,
            top_p: 0.8,
            frequency_penalty: 0,
            presence_penalty: 0
        };

        // Model-specific optimizations
        const modelOptimizations = {
            'gpt-4o-mini': {
                temperature: 0.3,
                max_tokens: 1024,
                top_p: 0.7
            },
            'gpt-4o': {
                temperature: 0.4,
                max_tokens: 1024,
                top_p: 0.8
            },
            'gpt-3.5-turbo': {
                temperature: 0.3,
                max_tokens: 1024,
                top_p: 0.7
            },
            'claude-3-haiku': {
                temperature: 0.4,
                max_tokens: 1024,
                top_p: 0.8
            }
        };

        // Apply model-specific optimizations
        const modelParams = modelOptimizations[model] || {};

        // Speed mode: ultra-fast settings
        if (options.speed === 'ultra-fast') {
            return {
                ...baseParams,
                temperature: 0.2,
                max_tokens: 256,
                top_p: 0.6
            };
        }

        // Speed mode: fast settings
        if (options.speed === 'fast') {
            return {
                ...baseParams,
                temperature: 0.3,
                max_tokens: 512,
                top_p: 0.7
            };
        }

        // Default: balanced fast settings
        return {
            ...baseParams,
            ...modelParams
        };
    }

    /**
     * Get fastest alternative model
     * @param {string} currentModel - Current model
     * @returns {string} Fastest alternative model
     */
    _getFastestModel(currentModel) {
        const fastModels = ['gpt-4o-mini', 'gpt-3.5-turbo', 'gpt-4o'];
        return fastModels.find(model => model !== currentModel) || 'gpt-4o-mini';
    }

    /**
     * Generate image
     * @param {string} provider - Provider name
     * @param {string} model - Model name
     * @param {string} prompt - Image prompt
     * @param {Object} options - Additional options
     * @returns {Promise<Object>} Image generation response
     */
    async generateImage(provider, model, prompt, options = {}) {
        if (!this.providers[provider]) {
            throw new Error(`Provider '${provider}' not available`);
        }

        if (!this.providers[provider].images) {
            throw new Error(`Provider '${provider}' does not support image generation`);
        }

        const params = {
            model: model,
            prompt: prompt,
            ...options
        };

        try {
            const response = await this.providers[provider].images.generate(params);
            return response;
        } catch (error) {
            console.error(`Image generation failed for ${provider}:`, error);
            throw error;
        }
    }

    /**
     * Format prompt into messages array
     * @param {string|Array} prompt - Prompt string or messages array
     * @returns {Array} Messages array
     */
    _formatMessages(prompt) {
        if (Array.isArray(prompt)) {
            return prompt;
        }
        
        return [{ role: 'user', content: prompt }];
    }
}

// Create global instance
const aiApp = new AIApp();

/**
 * Simple chat function
 * @param {string} provider - Provider name
 * @param {string} model - Model name
 * @param {string|Array} prompt - Prompt
 * @param {Object} options - Options
 * @returns {Promise<Object>} Response
 */
export async function chat(provider, model, prompt, options = {}) {
    return await aiApp.chat(provider, model, prompt, options);
}

/**
 * Streaming chat function with built-in optimizations
 * @param {string} provider - Provider name
 * @param {string} model - Model name
 * @param {string|Array} prompt - Prompt
 * @param {Function} onChunk - Chunk callback
 * @param {Object} options - Options (speed: 'ultra-fast'|'fast'|'balanced', showStats: true, autoRetry: true)
 */
export async function chatStream(provider, model, prompt, onChunk, options = {}) {
    return await aiApp.chatStream(provider, model, prompt, onChunk, options);
}

/**
 * Ultra-fast streaming chat (optimized for speed)
 * @param {string} provider - Provider name
 * @param {string} model - Model name
 * @param {string|Array} prompt - Prompt
 * @param {Function} onChunk - Chunk callback
 * @param {Object} options - Additional options
 */
export async function fastChatStream(provider, model, prompt, onChunk, options = {}) {
    return await aiApp.chatStream(provider, model, prompt, onChunk, {
        speed: 'ultra-fast',
        showStats: true,
        autoRetry: true,
        ...options
    });
}

/**
 * Image generation function
 * @param {string} provider - Provider name
 * @param {string} model - Model name
 * @param {string} prompt - Image prompt
 * @param {Object} options - Options
 * @returns {Promise<Object>} Response
 */
export async function generateImage(provider, model, prompt, options = {}) {
    return await aiApp.generateImage(provider, model, prompt, options);
}

/**
 * Get available providers
 * @returns {string[]} Provider names
 */
export function getProviders() {
    return aiApp.getAvailableProviders();
}

/**
 * Get models for a provider
 * @param {string} provider - Provider name
 * @returns {Promise<Array>} Models array
 */
export async function getModels(provider) {
    return await aiApp.getModels(provider);
}

// Export the main class and instance
export { AIApp, aiApp };
export default aiApp;
